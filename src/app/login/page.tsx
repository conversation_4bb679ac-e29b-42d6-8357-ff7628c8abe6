"use client";

import { <PERSON>, PasswordField } from "@/components/Field";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Form } from "@/components/ui/form";
import { Separator } from "@/components/ui/separator";
import { authClient } from "@/lib/auth-client";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { useRouter, useSearchParams } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { FcGoogle } from "react-icons/fc";
import { toast } from "sonner";
import z from "zod";

// import { Separator } from "@/components/ui/separator"
const formSchema = z.object({
  name: z.string().nonempty(),
  email: z.email(),
  password: z.string().min(8),
  confirmPassword: z.string().min(8),
});

export default function Page() {
  const searchParams = useSearchParams();
  const t = useTranslations();
  const [isSignUp, setIsSignUp] = useState(searchParams.get("onboarding") === "true");
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: (values, context, options) => {
      if (!isSignUp) {
        return zodResolver(formSchema.omit({ confirmPassword: true, name: true }) as typeof formSchema)(
          values,
          context,
          options,
        );
      }
      return zodResolver(formSchema)(values, context, options);
    },
    defaultValues: {
      name: "",
      email: "",
      password: "",
      confirmPassword: "",
    },
  });

  const handleSubmit = async (formData: z.infer<typeof formSchema>) => {
    setIsLoading(true);
    try {
      if (isSignUp) {
        if (formData.password !== formData.confirmPassword) {
          toast.error(t("Passwords don't match!"));
          return;
        }
        // await signUp(formData.name, formData.email, formData.password)
        await authClient.signUp.email(
          {
            email: formData.email,
            password: formData.password,
            name: formData.name,
          },
          {
            onRequest: () => {
              //show loading
            },
            onSuccess: () => {
              const redirect = searchParams.get("redirect");
              router.push(redirect ? decodeURIComponent(redirect) : "/");
            },
            onError: (ctx) => {
              // display the error message
              console.log(ctx.error);
              toast.error(t("Failed to create account"));
            },
          },
        );
      } else {
        // await signIn(formData.email, formData.password)
        await authClient.signIn.email(
          {
            email: formData.email,
            password: formData.password,
            /**
             * remember the user session after the browser is closed.
             * @default true
             */
            rememberMe: false,
          },
          {
            onSuccess: () => {
              const redirect = searchParams.get("redirect");
              router.push(redirect ? decodeURIComponent(redirect) : "/");
            },
            onError: (ctx) => {
              // display the error message
              console.log(ctx.error);
              toast.error(t("Failed to sign in"));
            },
          },
        );
      }
    } catch (error) {
      console.error("Authentication error:", error);
      toast.error(t("An error occurred"));
    } finally {
      setIsLoading(false);
    }
  };

  const toggleMode = () => {
    setIsSignUp(!isSignUp);
    form.reset();
  };

  return (
    <div className="flex items-center justify-center px-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold text-center">
            {isSignUp ? t("Create an account") : "Login"}
          </CardTitle>
        </CardHeader>

        <CardContent className="space-y-4">
          <div className="grid gap-4">
            <Button
              variant="outline"
              onClick={async () => {
                await authClient.signIn.social({
                  provider: "google",
                });
              }}
            >
              <FcGoogle className="mr-2 h-4 w-4" />
              {t("Sign in with Google")}
            </Button>
          </div>

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <Separator className="w-full" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">{t("Or continue with")}</span>
            </div>
          </div>

          <Accordion type="single" collapsible>
            <AccordionItem value="item-1">
              <AccordionTrigger className="text-center">
                <span className="mx-auto ps-8">{t("Email")}</span>
              </AccordionTrigger>
              <AccordionContent>
                <p className="text-center mb-4">
                  {isSignUp
                    ? t("Enter your information to create your account")
                    : t("Enter your credentials to access your account")}
                </p>
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
                    {isSignUp && (
                      <Field
                        control={form.control}
                        name="name"
                        label={t("Full Name")}
                        input={{ props: { placeholder: "John Doe" } }}
                      />
                    )}
                    <Field
                      control={form.control}
                      name="email"
                      label={t("Email")}
                      input={{ props: { placeholder: "<EMAIL>", type: "email" } }}
                    />

                    <PasswordField
                      control={form.control}
                      name="password"
                      label={t("Password")}
                      input={{ props: { placeholder: t("Enter your password") } }}
                    />

                    {isSignUp && (
                      <PasswordField
                        control={form.control}
                        name="confirmPassword"
                        label={t("Confirm Password")}
                        input={{ props: { placeholder: t("Confirm your password") } }}
                      />
                    )}

                    {!isSignUp && (
                      <div className="flex items-center justify-end">
                        <Button variant="link" className="px-0 font-normal">
                          {t("Forgot your password?")}
                        </Button>
                      </div>
                    )}

                    <Button type="submit" className="w-full" disabled={isLoading} loading={isLoading}>
                      {isSignUp ? t("Create Account") : t("Sign In")}
                    </Button>
                  </form>
                </Form>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </CardContent>

        <CardFooter>
          <div className="text-center text-sm text-muted-foreground w-full">
            {isSignUp ? t("Already have an account?") : t("Don't have an account?")}{" "}
            <Button variant="link" className="px-0 font-normal" onClick={toggleMode}>
              {isSignUp ? t("Sign in") : t("Sign up")}
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
