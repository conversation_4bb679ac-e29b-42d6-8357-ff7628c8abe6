import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card";
import type { PackageItem, SubscriptionItem } from "@/database/drizzle/schema/packages";
import { serverFn } from "@/lib/serverFn";
import { CheckCircle } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { toast } from "sonner";
import { FeatureDetails } from "../admin/packages/[id]/FeatureDetails";
import { onCreateStripeCheckout } from "./createStripeCheckout.action";
import { Data } from "./data";

interface PackageCardProps {
  package: Data["packages"][number];
  currentSubscription?: (SubscriptionItem & { package: PackageItem }) | null;
  user: { id: string; email: string; name?: string } | null;
}

export function PackageCard({ package: pkg, currentSubscription, user }: PackageCardProps) {
  const t = useTranslations();
  const [isLoading, setIsLoading] = useState(false);

  const isCurrentPlan = currentSubscription?.packageId === pkg.id;
  const isFree = parseFloat(pkg.price) === 0;
  const hasActiveSubscription =
    currentSubscription && (currentSubscription.status === "active" || currentSubscription.status === "trialing");

  const handleSubscribe = async () => {
    if (!user) {
      // Redirect to login
      window.location.href = "/login";
      return;
    }

    if (isFree) {
      // Free plan doesn't need Stripe checkout
      // You might want to handle free plan subscription here
      return;
    }

    setIsLoading(true);
    try {
      const result = await serverFn(t, onCreateStripeCheckout, {
        packageId: pkg.id,
        userId: user.id,
        userEmail: user.email,
        userName: user.name,
      });

      // Redirect to Stripe checkout
      if (result?.checkoutUrl) {
        window.location.href = result.checkoutUrl;
      }
    } catch (error) {
      console.error("Failed to create checkout session:", error);
      toast.error(t("Failed to start checkout Please try again"));
    } finally {
      setIsLoading(false);
    }
  };

  const formatPrice = (price: string, interval: string) => {
    const amount = parseFloat(price);
    if (amount === 0) return t("Free");

    const formatted = new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);

    return `${formatted}/${interval === "month" ? t("month") : t("year")}`;
  };

  const getButtonText = () => {
    if (isCurrentPlan && hasActiveSubscription) {
      return t("Current Plan");
    }
    if (isFree) {
      return t("Get Started");
    }
    if (hasActiveSubscription) {
      return t("Switch Plan");
    }
    return t("Subscribe");
  };

  const isButtonDisabled = isLoading || (isCurrentPlan && !!hasActiveSubscription);

  return (
    <Card
      className={`relative ${isCurrentPlan ? "ring-2 ring-blue-500" : ""} ${pkg.name === "Pro" ? "border-blue-500 shadow-lg" : ""}`}
    >
      {pkg.name === "Pro" && (
        <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-blue-500">{t("Most Popular")}</Badge>
      )}

      <CardHeader className="text-center">
        <CardTitle className="text-2xl">{pkg.name}</CardTitle>
        <CardDescription>{pkg.description}</CardDescription>
        <div className="mt-4">
          <span className="text-4xl font-bold">{formatPrice(pkg.price, pkg.billingInterval)}</span>
        </div>
      </CardHeader>

      <CardContent>
        <div className="space-y-2">
          {pkg.features.map((feature) => (
            <div key={feature.type} className="border-l-2 border-primary/20 pl-3">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="font-medium">{t(feature.type)}</span>
              </div>
              <FeatureDetails feature={feature} />
            </div>
          ))}
        </div>
      </CardContent>

      <CardFooter className="mt-auto">
        <Button
          className="w-full"
          onClick={handleSubscribe}
          disabled={isButtonDisabled}
          loading={isLoading}
          variant={isCurrentPlan && hasActiveSubscription ? "outline" : "default"}
        >
          {getButtonText()}
        </Button>
      </CardFooter>
    </Card>
  );
}
