"use client";

import { Field, MultipleSelectorField, SelectField } from "@/components/Field";
import { Button } from "@/components/ui/button";
import { Dialog, DialogClose, DialogContent, DialogFooter, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Form } from "@/components/ui/form";
import { FeatureItem } from "@/database/drizzle/schema/packages";
import { Feature, features, featureSchema, FeatureType } from "@/lib/features";
import { serverFn } from "@/lib/serverFn";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { onCreateFeature, onUpdateFeature } from "./actions";

interface FeatureFormDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  packageId: string;
  feature?: FeatureItem;
  existingFeatureTypes: FeatureType[];
  onSuccess: () => void;
}

export function FeatureFormDialog({
  isOpen,
  onOpenChange,
  packageId,
  feature,
  existingFeatureTypes,
  onSuccess,
}: FeatureFormDialogProps) {
  const t = useTranslations();
  const [isLoading, setIsLoading] = useState(false);
  const isEditing = !!feature;

  const form = useForm<Feature>({
    resolver: zodResolver(featureSchema),
    defaultValues: getDefaultValues(feature),
  });

  const selectedType = form.watch("type") as FeatureType;

  // Get available feature types (exclude existing ones when creating)
  const availableTypes = isEditing ? [feature!.type] : features.filter((type) => !existingFeatureTypes.includes(type));

  const onSubmit = async (values: Feature) => {
    setIsLoading(true);

    try {
      let result;
      if (isEditing) {
        result = await serverFn(t, onUpdateFeature, packageId, feature!.type, values);
      } else {
        result = await serverFn(t, onCreateFeature, packageId, values);
      }

      if (result?.success) {
        toast.success(t(isEditing ? "Feature updated successfully" : "Feature created successfully"));
        onSuccess();
        onOpenChange(false);
        form.reset();
      }
    } catch (error) {
      console.error("Error submitting feature:", error);
      toast.error(t("An error occurred"));
    }

    setIsLoading(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{isEditing ? t("Edit Feature") : t("Add Feature")}</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <SelectField
              name="type"
              label={t("Feature Type")}
              control={form.control}
              options={availableTypes.map((type) => ({ value: type, label: type }))}
              disabled={isEditing}
            />

            {selectedType && renderMetaFields(selectedType, form, t)}

            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" variant="outline" disabled={isLoading}>
                  {t("Cancel")}
                </Button>
              </DialogClose>
              <Button type="submit" disabled={isLoading} loading={isLoading}>
                {isEditing ? t("Update Feature") : t("Create Feature")}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

function getDefaultValues(feature?: FeatureItem): Feature {
  if (!feature) {
    return { type: "Premium templates", meta: {} };
  }

  const parsedFeature = featureSchema.safeParse(feature);
  if (!parsedFeature.success) {
    return (
      {
        "Premium templates": { type: "Premium templates", meta: {} },
        "24/7 priority support": { type: "24/7 priority support", meta: { responseTime: 24, channels: [] } },
        "Advanced analytics": { type: "Advanced analytics", meta: {} },
        "Team collaboration": { type: "Team collaboration", meta: { maxMembers: 0, roles: [] } },
        "API access": { type: "API access", meta: { rateLimit: 0 } },
        "Custom integrations": { type: "Custom integrations", meta: {} },
      } as Record<FeatureType, Feature>
    )[feature.type];
  }

  return parsedFeature.data;
}

function renderMetaFields(type: FeatureType, form: any, t: any) {
  switch (type) {
    case "24/7 priority support":
      return (
        <div className="space-y-4">
          <Field
            name="meta.responseTime"
            label={t("Response Time (hours)")}
            control={form.control}
            input={{ props: { type: "number", min: 1, placeholder: "24" } }}
          />
          <MultipleSelectorField
            control={form.control}
            name="meta.channels"
            label={t("Support Channels")}
            inputProps={{
              defaultOptions: [
                { value: "chat", label: t("Chat") },
                { value: "email", label: t("Email") },
                { value: "phone", label: t("Phone") },
              ],
            }}
          />
        </div>
      );

    case "Team collaboration":
      return (
        <div className="space-y-4">
          <Field
            name="meta.maxMembers"
            label={t("Maximum Members")}
            control={form.control}
            input={{ props: { type: "number", min: 1, placeholder: "5" } }}
          />
          <MultipleSelectorField
            control={form.control}
            name="meta.roles"
            label={t("Available Roles")}
            inputProps={{
              placeholder: t("Select roles"),
              options: [
                { value: "admin", label: t("Admin") },
                { value: "editor", label: t("Editor") },
                { value: "viewer", label: t("Viewer") },
              ],
            }}
          />
        </div>
      );

    case "API access":
      return (
        <Field
          name="meta.rateLimit"
          label={t("Rate Limit (requests/minute)")}
          control={form.control}
          input={{ props: { type: "number", min: 1, placeholder: "1000" } }}
        />
      );

    default:
      return (
        <div className="text-sm text-muted-foreground">
          {t("This feature type has no additional configuration options")}
        </div>
      );
  }
}
