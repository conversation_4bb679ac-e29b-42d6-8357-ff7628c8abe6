"use server";

import { VideoIdeaInsert, VideoInsert } from "@/database/drizzle/schema/videos";
import { addJob, QGenerateVideo } from "@/jobs/queues";
import { ai } from "@/lib/ai";
import { getVoices } from "@/lib/elevenlabs";
import { errors } from "@/lib/errors";
import { getDataContext } from "@/lib/getDataContext";
import { getAIOptions } from "@/lib/prompts";
import * as drizzleQueries from "../../database/drizzle/queries/videos";

export async function onNewVideo(data: VideoInsert, delay = 0) {
  const context = await getDataContext();

  if (!context.session?.user.id) return { error: errors.NotAuthenticated };

  data.voiceId ||= "tnSpp4vdxKPjI9w0GnoV";
  data.userId = context.session.user.id;
  data.scheduledAt = new Date(Date.now() + delay);

  let record = (await drizzleQueries.insertVideo(context.db, data).returning())[0];

  const job = await addJob(context.db, context.session.user.id, QGenerateVideo, { videoId: record.id }, delay);
  if (job?.id)
    record = (
      await drizzleQueries
        .updateVideo(context.db, record.id, {
          jobId: job?.id,
        })
        .returning()
    )[0];

  return record;
}

export async function onGetTargetSuggestion(theme: string) {
  const { output } = await ai.getStructuredOutput(await getAIOptions("TargetSuggestion", { theme }));

  return output;
}

export async function onGetVideoTitles(data: VideoIdeaInsert) {
  const { output } = await ai.getStructuredOutput(
    await getAIOptions("VideoTitles", {
      noOfVideos: data.days * data.frequency,
      theme: data.theme,
      target: data.target,
    }),
  );

  return output;
}

export async function onSaveVideoIdea(data: VideoIdeaInsert) {
  const context = await getDataContext();
  const videoIdea = (await drizzleQueries.insertVideoIdea(context.db, data).returning())[0];
  const interval = (24 * 60 * 60 * 1000) / data.frequency;

  const { theme, target, userId } = videoIdea;

  await Promise.all(
    data.videos?.map(async (x, i) => {
      await onNewVideo({ title: x.title, theme, target, userId, videoIdeaId: videoIdea.id }, i * interval);
    }) || [],
  );
}

export async function onGetMoreVoices(...args: Parameters<typeof getVoices>) {
  return await getVoices(...args);
}
