import type { Metadata } from "next";
import localFont from "next/font/local";
import { cookies } from "next/headers";

import LayoutDefault from "@/components/LayoutDefault";

import "./globals.css";

import { NextIntlClientProvider } from "next-intl";
import Provider from "./providers";

const geistSans = localFont({
  src: "../assets/fonts/Geist/Geist-VariableFont_wght.ttf",
  variable: "--font-geist-sans",
});

const geistMono = localFont({
  src: "../assets/fonts/Geist_Mono/GeistMono-VariableFont_wght.ttf",
  variable: "--font-geist-mono",
});

export const metadata: Metadata = {
  title: "Shorts",
  description: "Shorts",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const serverCookies = await cookies();
  const lang = serverCookies.get("locale")?.value || "en";

  return (
    <html lang={lang}>
      <body className={`${geistSans.variable} ${geistMono.variable} font-sans antialiased`}>
        <NextIntlClientProvider>
          <Provider>
            <LayoutDefault>{children}</LayoutDefault>
          </Provider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
