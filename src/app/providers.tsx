"use client";

import { Toaster } from "@/components/ui/sonner";
import { PageContext } from "@/lib/page-context";
import { useLocale } from "next-intl";
import { useEffect } from "react";
import z from "zod";
import { en, fr } from "zod/locales";

export default function Provider({ children }: { children: React.ReactNode }) {
  const locale = useLocale();

  useEffect(() => {
    z.config(locale === "en" ? en() : fr());
  }, [locale]);

  return (
    <>
      <PageContext value={{}}>{children}</PageContext>
      <Toaster position="top-center" />
    </>
  );
}
