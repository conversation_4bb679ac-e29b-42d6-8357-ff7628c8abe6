"use client";

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ield } from "@/components/Field";
import { Heading } from "@/components/Heading";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Form } from "@/components/ui/form";
import { serverFn } from "@/lib/serverFn";
import { zodResolver } from "@hookform/resolvers/zod";
import { EditIcon, ShareIcon, VideoOffIcon } from "lucide-react";
import { useFormatter, useTranslations } from "next-intl";
import Link from "next/link";
import { useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import z from "zod";
import { platforms } from "../account/social-accounts/platforms";
import { onShare } from "./VideoList.action";
import { useData } from "./data.client";

export function VideoList() {
  const t = useTranslations();
  const format = useFormatter();
  const { videos } = useData();

  if (!videos?.length) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <VideoOffIcon className="h-16 w-16 text-gray-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-700">{t("No videos found")}</h2>
          <p className="text-gray-500 mt-2">{t("You haven't generated any videos yet")}</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Heading title={t("Videos")} description={t("Manage your videos")} />

      <div className="space-y-4">
        {videos.map((video) => (
          <Card key={video.id} className="flex-row overflow-clip py-0 gap-0 relative">
            {video.thumbnailUrl ? (
              // eslint-disable-next-line @next/next/no-img-element
              <img src={video.thumbnailUrl} alt={video.title || ""} className="basis-32 max-w-32" />
            ) : (
              <div className="aspect-[9/16] bg-gray-50 flex items-center justify-center basis-32 shrink grow-0">
                <VideoOffIcon className="h-16 w-16 text-gray-500" />
              </div>
            )}
            <div className="py-4 space-y-4 basis-0 grow shrink">
              <CardHeader>
                <CardTitle>
                  <Link href={`/videos/${video.id}/`} className="stretched-link">
                    {video.title}
                  </Link>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <CardDescription>{video.description}</CardDescription>
                <div className="flex gap-2">
                  <Badge>{video.visibility}</Badge>
                  <Badge>
                    {video.durationSeconds} {t("seconds")}
                  </Badge>
                  <Badge>{video.target}</Badge>
                </div>
                {video.scheduledAt && (
                  <div className="text-sm flex gap-2">
                    <span className="text-muted-foreground">{t("Scheduled at")}:</span>
                    {format.relativeTime(new Date(video.scheduledAt), new Date())}
                  </div>
                )}
                <div className="flex gap-2 relative z-10">
                  <Button asChild>
                    <Link href={`/videos/${video.id}/edit`}>
                      <EditIcon /> <span className="sr-only">{t("Edit")}</span>
                      {t("Edit")}
                    </Link>
                  </Button>
                  <ShareDialog videoId={video.id} />
                </div>
              </CardContent>
            </div>
          </Card>
        ))}
      </div>
    </>
  );
}

const formSchema = z.object({
  platforms: z.array(z.boolean()).length(platforms.length),
  scheduleAt: z.date(),
});

function ShareDialog({ videoId }: { videoId: string }) {
  const t = useTranslations();
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      platforms: platforms.map(() => false),
      scheduleAt: new Date(),
    },
  });
  const { getlateProfile } = useData();
  const [postNow, setPostNow] = useState<"indeterminate" | boolean>(true);
  const [isSharing, setIsSharing] = useState(false);
  const accountsMap = useMemo(() => {
    return new Map((getlateProfile?.accounts || []).map((x) => [x.platform.toLowerCase(), x]));
  }, [getlateProfile?.accounts]);

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button>
          <ShareIcon />
          {t("Share")}
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t("Share")}</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(async function onSubmit(values: z.infer<typeof formSchema>) {
              setIsSharing(true);
              await serverFn(
                t,
                onShare,
                videoId,
                platforms.filter((x, i) => values.platforms[i]).map((x) => x.name),
                postNow === true ? undefined : new Date(values.scheduleAt).toISOString(),
              );
              setIsSharing(false);
            })}
            className="space-y-4"
          >
            {platforms.map((platform) => (
              <CheckboxField
                key={platform.name}
                name={`platforms.${platforms.indexOf(platform)}`}
                label={
                  <>
                    {<platform.icon style={{ color: platform.color }} />} {platform.name}
                  </>
                }
                disabled={!accountsMap.has(platform.name.toLowerCase())}
              />
            ))}
            <label className="flex gap-2">
              <Checkbox checked={postNow} onCheckedChange={setPostNow} /> {t("Post now")}
            </label>
            <DateTimeField name="scheduleAt" label="Schedule at" disabled={postNow === true} />
            <Button type="submit" disabled={isSharing} loading={isSharing}>
              {t("Share")}
            </Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
