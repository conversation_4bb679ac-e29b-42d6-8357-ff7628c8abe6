"use server";

import { ai } from "@/lib/ai";
import { getAIOptions } from "@/lib/prompts";
import { onNewVideo } from "../(home)/CreateVideoIdeaForm.action";

export async function onGenerateVideoIdea(description: string) {
  const { output } = await ai.getStructuredOutput(
    await getAIOptions("VideoIdeaFromDescription", { description })
  );

  return output;
}

export async function onCreateVideoFromDescription(data: {
  title: string;
  theme: string;
  target: string;
}) {
  return await onNewVideo({
    title: data.title,
    theme: data.theme,
    target: data.target,
  });
}
