"use client";

import { SelectField } from "@/components/Field";
import Player from "@/components/Player";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form } from "@/components/ui/form";
import { serverFn } from "@/lib/serverFn";
import { zodResolver } from "@hookform/resolvers/zod";
import { EditIcon, EyeIcon, EyeOffIcon, VideoOffIcon } from "lucide-react";
import { useFormatter, useTranslations } from "next-intl";
import Link from "next/link";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { useData } from "./data.client";
import { onGenerateVideoThumbnail, onUpdateVideoVisibility } from "./VideoPage.action";

const visibilityFormSchema = z.object({
  visibility: z.enum(["public", "private"]),
});

export function VideoPage() {
  const { video, session } = useData();
  const t = useTranslations();
  const format = useFormatter();
  const [isUpdating, setIsUpdating] = useState(false);
  const [isGTLoading, setIsGTLoading] = useState(false);

  const form = useForm<z.infer<typeof visibilityFormSchema>>({
    resolver: zodResolver(visibilityFormSchema),
    defaultValues: {
      visibility: video?.visibility || "public",
    },
  });

  const isOwner = session?.user && video?.userId === session.user.id;

  const onSubmit = async (values: z.infer<typeof visibilityFormSchema>) => {
    if (!video || !isOwner) return;

    setIsUpdating(true);
    const res = await serverFn(t, onUpdateVideoVisibility, video.id, values.visibility);
    if (res?.success) toast.success(t("Video visibility updated successfully"));
    setIsUpdating(false);
  };

  if (!video) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <VideoOffIcon className="h-16 w-16 text-gray-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-700">{t("Video not found")}</h2>
          <p className="text-gray-500 mt-2">{t("The video you're looking for doesn't exist or has been removed^")}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Video Player Section */}
        <div className="space-y-4">
          <div className="rounded-2xl overflow-hidden">
            {video.videoUrl ? (
              <Player src={video.videoUrl} className="aspect-[9/16] bg-gray-50" />
            ) : (
              <div className="aspect-[9/16] bg-gray-50 flex items-center justify-center rounded-2xl">
                <div className="text-center">
                  <VideoOffIcon className="h-16 w-16 text-gray-500 mx-auto mb-4" />
                  <p className="text-gray-500">{t("Video is being generated^^^")}</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Video Details Section */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  {video.visibility === "private" ? (
                    <EyeOffIcon className="h-5 w-5 text-gray-500" />
                  ) : (
                    <EyeIcon className="h-5 w-5 text-gray-500" />
                  )}
                  {video.theme}
                </CardTitle>
                {isOwner && (
                  <Button asChild variant="outline" size="sm">
                    <Link href={`/videos/${video.id}/edit`}>
                      <EditIcon className="h-4 w-4 mr-2" />
                      {t("Edit")}
                    </Link>
                  </Button>
                )}
              </div>
              <CardDescription>
                {video.visibility === "private"
                  ? t("This video is private and only visible to you")
                  : t("This video is public and can be viewed by anyone")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold text-sm text-gray-700 mb-2">{t("Target Audience")}</h3>
                  <p className="text-gray-600">{video.target}</p>
                </div>

                <div>
                  <h3 className="font-semibold text-sm text-gray-700 mb-2">{t("Duration")}</h3>
                  <p className="text-gray-600">
                    {video.durationSeconds} {t("seconds")}
                  </p>
                </div>

                <div>
                  <h3 className="font-semibold text-sm text-gray-700 mb-2">{t("Created")}</h3>
                  <p className="text-gray-600">{format.relativeTime(new Date(video.createdAt), new Date())}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Visibility Control - Only show to owner */}
          {isOwner && (
            <Card>
              <CardHeader>
                <CardTitle>{t("Privacy Settings")}</CardTitle>
                <CardDescription>{t("Control who can view this video")}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                    <SelectField
                      name="visibility"
                      label={t("Visibility")}
                      control={form.control}
                      options={[
                        {
                          value: "public",
                          label: t("Public - Anyone can view this video"),
                        },
                        {
                          value: "private",
                          label: t("Private - Only you can view this video"),
                        },
                      ]}
                    />
                    <Button
                      type="submit"
                      disabled={isUpdating || !form.formState.isDirty}
                      loading={isUpdating}
                      className="w-full"
                    >
                      {t("Update Visibility")}
                    </Button>
                  </form>
                </Form>
                {video.videoUrl && (
                  <>
                    {video.thumbnailUrl && (
                      // eslint-disable-next-line @next/next/no-img-element
                      <img
                        src={video.thumbnailUrl}
                        alt="Thumbnail"
                        className="w-full aspect-[9/16] object-cover rounded-xl"
                      />
                    )}
                    <Button
                      type="button"
                      className="w-full"
                      loading={isGTLoading}
                      disabled={isGTLoading}
                      onClick={async () => {
                        setIsGTLoading(true);
                        await serverFn(t, onGenerateVideoThumbnail, video.id);
                        setIsGTLoading(false);
                      }}
                    >
                      {t("Generate Thumbnail")}
                    </Button>
                  </>
                )}
              </CardContent>
            </Card>
          )}

          {/* Video Transcript Preview */}
          {video.transcript && (
            <Card>
              <CardHeader>
                <CardTitle>{t("Transcript")}</CardTitle>
                <CardDescription>{t("Generated content for this video")}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 max-h-60 overflow-y-auto">
                  {video.transcript.sections.map((section, index) => (
                    <div key={index} className="border-l-2 border-gray-200 pl-4">
                      <p className="text-sm text-gray-600 mb-1">
                        {t("Section")} {index + 1}
                      </p>
                      <div className="text-sm" dangerouslySetInnerHTML={{ __html: section.voiceover }}></div>
                      {section.imageDescription && (
                        <p className="text-xs text-gray-500 mt-1 italic">
                          {t("Image")}: {section.imageDescription}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
