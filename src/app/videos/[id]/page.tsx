import type { Metadata } from "next";
import { getData } from "./data";
import { DataContainer } from "./data.client";
import { VideoPage } from "./VideoPage";

export async function generateMetadata({ params }: { params: Promise<{ id: string }> }): Promise<Metadata> {
  const data = await getData({ params });

  const title = data.video.title || data.video.theme;
  const description = data.video.description || "";
  const videoUrl = data.video.videoUrl;

  return {
    title,
    description,
    openGraph: videoUrl
      ? {
          title,
          description,
          type: "video.other",
          videos: {
            url: videoUrl,
            type: "video/mp4",
            width: 576,
            height: 1024,
          },
          images: data.video.thumbnailUrl || undefined,
        }
      : undefined,
    twitter: videoUrl
      ? {
          card: "player",
          title,
          description,
          players: {
            playerUrl: `${process.env.BASE_URL}/videos/${(await params).id}`,
            streamUrl: videoUrl,
            width: 576,
            height: 1024,
          },
        }
      : undefined,
  };
}

export default async function Page({ params }: { params: Promise<{ id: string }> }) {
  const data = await getData({ params });

  return (
    <DataContainer data={data}>
      <VideoPage />
    </DataContainer>
  );
}
