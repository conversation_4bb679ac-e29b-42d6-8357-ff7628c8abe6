"use client";

import { <PERSON>, <PERSON>Field, <PERSON>Field } from "@/components/Field";
import { ImageField } from "@/components/ImageField";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Form } from "@/components/ui/form";
import { VideoItem, videoSettingsSchema } from "@/database/drizzle/schema/videos";
import { serverFn } from "@/lib/serverFn";
import { cn } from "@/lib/utils";
import type { GetVoicesV2Response } from "@elevenlabs/elevenlabs-js/api/types";
import { zodResolver } from "@hookform/resolvers/zod";
import { toMerged } from "es-toolkit";
import { htmlToText } from "html-to-text";
import { ArrowDownIcon, ArrowUpIcon, PlusIcon, Settings, TrashIcon, Volume2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { z } from "zod";
import { useData } from "./data.client";
import { defaultVideoSettings } from "./defaultVideoSettings";
import { onGetMoreVoices, onUpdateAndGenerateVideo } from "./VideoEditForm.action";

const sectionSchema = z.object({
  imageDescription: z.string(),
  voiceover: z.string(),
  imageUrl: z.string().optional(),
});

const formSchema = z.object({
  title: z.string().nonempty(),
  description: z.string(),
  theme: z.string().nonempty(),
  voiceId: z.string(),
  videoSettings: videoSettingsSchema,
  transcript: z.object({ sections: z.array(sectionSchema) }),
});

export function VideoEditForm() {
  const { video, voices: _voices } = useData();
  const [voices, setVoices] = useState(_voices);
  const t = useTranslations();
  const [isGenerating, setIsGenerating] = useState(false);
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: video?.title || "",
      description: video?.description || "",
      theme: video?.theme,
      transcript: video?.transcript as z.infer<typeof formSchema>["transcript"],
      videoSettings: video?.videoSettings ? toMerged(defaultVideoSettings, video.videoSettings) : defaultVideoSettings,
      voiceId: video?.voiceId || "",
    },
  });
  const { fields, append, remove, swap } = useFieldArray({
    control: form.control,
    name: "transcript.sections",
  });
  const [selectedSectionIndex, setSelectedSectionIndex] = useState<number>(0);
  const router = useRouter();

  async function onSubmit(values: z.infer<typeof formSchema>) {
    if (isGenerating || !video) return;
    setIsGenerating(true);
    await serverFn(t, onUpdateAndGenerateVideo, video.id, values);
    router.push(`/videos`);
    setIsGenerating(false);
  }

  return (
    <>
      <div className="flex items-center gap-4 mb-4">
        <h1 className="text-2xl font-bold mb-4 me-auto">{t("Edit")}</h1>
        <VideoSettings
          videoItem={form.getValues()}
          setVideoSettings={(videoSettings) => form.setValue("videoSettings", videoSettings)}
        />
      </div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <Field name="title" label={t("Title")} control={form.control} />
          <Field name="description" label={t("Description")} control={form.control} />
          <Field name="theme" label={t("Subject")} control={form.control} />
          <div className="grid gap-4 grid-cols-2">
            <div className="space-y-4">
              {fields.map((field, index) => (
                <div
                  key={field.id}
                  className={cn(
                    "flex gap-2 p-2 rounded-2xl",
                    selectedSectionIndex === index ? "bg-gray-100" : "bg-white",
                  )}
                  onClick={() => setSelectedSectionIndex(index)}
                >
                  <div className="space-y-2 flex-1">
                    <h2 className="text-lg font-bold">
                      {t("Section")} #{index + 1}
                    </h2>
                    <RichField
                      name={`transcript.sections.${index}.voiceover`}
                      label={t("Voiceover")}
                      control={form.control}
                    />
                    <div className="flex gap-2">
                      <Button
                        type="button"
                        onClick={() => swap(index - 1, index)}
                        variant="outline"
                        disabled={index === 0}
                      >
                        <ArrowUpIcon />
                      </Button>
                      <Button
                        type="button"
                        onClick={() => swap(index + 1, index)}
                        variant="outline"
                        disabled={index === fields.length - 1}
                      >
                        <ArrowDownIcon />
                      </Button>
                      <Button type="button" onClick={() => remove(index)} variant="destructive">
                        <TrashIcon />
                      </Button>
                    </div>
                  </div>
                  <ImageField
                    name={`transcript.sections.${index}.imageUrl`}
                    label={t("Image")}
                    control={form.control}
                  />
                </div>
              ))}
              <Button type="button" onClick={() => append({ imageDescription: "", voiceover: "" })} className="w-full">
                <PlusIcon /> {t("Add section")}
              </Button>
            </div>
            <div className="flex items-center justify-center">
              <div className="w-full max-w-72 aspect-[9/16] relative">
                {
                  // eslint-disable-next-line @next/next/no-img-element
                  <img
                    src={form.watch(`transcript.sections.${selectedSectionIndex}.imageUrl`)}
                    className="w-full h-full object-cover rounded-xl absolute"
                    alt={form.watch(`transcript.sections.${selectedSectionIndex}.imageDescription`)}
                  />
                }
                <div className="w-full h-full absolute flex text-center justify-center items-center text-3xl text-white">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" className="w-full h-auto">
                    <text
                      x="50%"
                      y="50%"
                      textAnchor="middle"
                      alignmentBaseline="middle"
                      fill={form.watch("videoSettings").text.font.color}
                      stroke={form.watch("videoSettings").text.stroke.color}
                      strokeWidth={form.watch("videoSettings").text.stroke.width / 2}
                      fontSize={form.watch("videoSettings").text.font.size}
                    >
                      {htmlToText(form.watch(`transcript.sections.${selectedSectionIndex}.voiceover`)).split(/\b/)[0]}
                    </text>
                  </svg>
                </div>
              </div>
            </div>
          </div>
          <Field control={form.control} name="voiceId" disabled label={t("Voice ID")} />
          {voices && (
            <SelectVoice voices={voices} setVoices={setVoices} setValue={(value) => form.setValue("voiceId", value)} />
          )}
          <hr />
          <Button type="submit" disabled={isGenerating}>
            {t("Generate new video")}
          </Button>
        </form>
      </Form>
    </>
  );
}

function SelectVoice({
  voices,
  setVoices,
  setValue,
}: {
  voices: GetVoicesV2Response;
  setVoices: (voices: GetVoicesV2Response) => void;
  setValue: (value: string) => void;
}) {
  const t = useTranslations();
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline">
          <Volume2 /> {t("Select Voice")}
        </Button>
      </DialogTrigger>
      <DialogContent className="h-screen">
        <DialogHeader>
          <DialogTitle>{t("Select Voice")}</DialogTitle>
        </DialogHeader>
        <div className="p-3 grid grid-cols-[repeat(auto-fill,minmax(300px,1fr))] bg-gray-200 gap-3 grow shrink basis-0 overflow-y-auto rounded-3xl">
          {voices.voices.map((voice) => (
            <div className="p-3 rounded-xl outline space-y-2 relative bg-white hover:bg-gray-50" key={voice.voiceId}>
              <h3 className="text-lg font-medium">{voice.name}</h3>
              <DialogClose
                type="button"
                onClick={() => setValue(voice.voiceId)}
                className="ms-auto text-xs font-medium stretched-link"
              >
                {voice.voiceId}
              </DialogClose>
              <audio controls className="relative z-10">
                <source src={voice.previewUrl} type="audio/mpeg" />
                {t("Your browser does not support the audio element")}
              </audio>
              <div className="text-sm text-secondary-foreground">{voice.description}</div>
            </div>
          ))}
        </div>
        <Button
          className="col-span-full"
          variant="outline"
          onClick={async () => {
            const moreVoices = await serverFn(t, onGetMoreVoices, {
              nextPageToken: voices.nextPageToken,
            });
            if (moreVoices)
              setVoices({
                ...moreVoices,
                voices: voices.voices.concat(moreVoices.voices),
              });
          }}
        >
          {t("Load more")}
        </Button>
        <DialogFooter>
          <DialogClose asChild>
            <Button variant="outline">{t("Cancel")}</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

function VideoSettings({
  videoItem,
  setVideoSettings,
}: {
  videoItem: Pick<VideoItem, "videoSettings">;
  setVideoSettings: (videoSettings: z.infer<typeof videoSettingsSchema>) => void;
}) {
  const t = useTranslations();
  const [open, setOpen] = useState(false);

  const form = useForm<z.infer<typeof videoSettingsSchema>>({
    resolver: zodResolver(videoSettingsSchema),
    defaultValues: toMerged(defaultVideoSettings, videoItem.videoSettings || {}),
  });

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">
          <Settings /> {t("Video Settings")}
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t("Video Settings")}</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit((values) => {
              console.log(values);
              setVideoSettings(values);
              setOpen(false);
            })}
            className="space-y-4 p-4"
          >
            <SelectField
              control={form.control}
              name="text.font.family"
              label={t("Font Family")}
              options={[
                { label: "Serif", value: "Arapey" },
                { label: "Sans", value: "Clear Sans" },
                { label: "Gothic", value: "Didact Gothic" },
                { label: "Marker", value: "Permanent Marker" },
              ]}
            />
            <Field
              control={form.control}
              name="text.font.color"
              label={t("Font color")}
              input={{ props: { type: "color" } }}
            />
            <SelectField
              control={form.control}
              name="text.font.size"
              label={t("Font size")}
              options={[
                { label: "72", value: "72" },
                { label: "64", value: "64" },
                { label: "56", value: "56" },
                { label: "48", value: "48" },
                { label: "40", value: "40" },
                { label: "32", value: "32" },
                { label: "24", value: "24" },
              ]}
            />
            <Field
              control={form.control}
              name="text.stroke.width"
              label={t("Text stroke width")}
              input={{ props: { type: "number", step: 1, min: 0 } }}
            />
            <Field
              control={form.control}
              name="text.stroke.color"
              label={t("Text stroke color")}
              input={{ props: { type: "color" } }}
            />
            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" variant="outline">
                  {t("Cancel")}
                </Button>
              </DialogClose>
              <Button type="submit">{t("Save")}</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
