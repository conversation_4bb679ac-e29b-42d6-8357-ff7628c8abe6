"use server";

import * as drizzleQueries from "@/database/drizzle/queries/videos";
import { addJob, QExtractThumbnail } from "@/jobs/queues";
import { errors } from "@/lib/errors";
import { getDataContext } from "@/lib/getDataContext";

export async function onUpdateVideoVisibility(videoId: string, visibility: "public" | "private") {
  const context = await getDataContext();

  if (!context.session?.user?.id) {
    return { error: errors.NotAuthenticated };
  }

  // First, get the video to verify ownership
  const [video] = await drizzleQueries.getVideo(context.db, videoId);

  if (!video) {
    return { error: errors.VideoNotFound };
  }

  if (video.userId !== context.session.user.id) {
    return { error: errors.NotAuthorized };
  }

  // Update the video visibility
  const updatedVideo = await drizzleQueries.updateVideo(context.db, videoId, {
    visibility,
  });

  return { success: true, video: updatedVideo };
}

export async function onGenerateVideoThumbnail(id: string) {
  const context = await getDataContext();

  if (!context.session?.user.id) return { error: errors.NotAuthenticated };

  // First, get the video to verify ownership
  const [video] = await drizzleQueries.getVideo(context.db, id);

  if (!video) {
    return { error: errors.VideoNotFound };
  }

  if (video.userId !== context.session.user.id) {
    return { error: errors.NotAuthorized };
  }

  await addJob(context.db, context.session.user.id, QExtractThumbnail, {
    videoId: id,
  });

  return { success: true };
}
