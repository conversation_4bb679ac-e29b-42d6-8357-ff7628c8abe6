"use client";

import { <PERSON>, TextareaField } from "@/components/Field";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogTrigger } from "@/components/ui/dialog";
import { Form } from "@/components/ui/form";
import { authClient } from "@/lib/auth-client";
import { serverFn } from "@/lib/serverFn";
import { zodResolver } from "@hookform/resolvers/zod";
import { PlusIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { onCreateVideoFromDescription, onGenerateVideoIdea } from "./CreateVideoForm.action";

const initialFormSchema = z.object({
  description: z.string().nonempty(),
});

const videoFormSchema = z.object({
  title: z.string().nonempty(),
  theme: z.string().nonempty(),
  target: z.string().nonempty(),
});

export function CreateVideoForm() {
  const t = useTranslations();
  const session = authClient.useSession();
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const [step, setStep] = useState<"description" | "details">("description");
  const [isLoading, setIsLoading] = useState(false);

  const initialForm = useForm<z.infer<typeof initialFormSchema>>({
    resolver: zodResolver(initialFormSchema),
    defaultValues: {
      description: "",
    },
  });

  const videoForm = useForm<z.infer<typeof videoFormSchema>>({
    resolver: zodResolver(videoFormSchema),
    defaultValues: {
      title: "",
      theme: "",
      target: "",
    },
  });

  async function onInitialSubmit(values: z.infer<typeof initialFormSchema>) {
    if (isLoading) return;
    setIsLoading(true);
    
    const res = await serverFn(t, onGenerateVideoIdea, values.description);
    if (res) {
      videoForm.setValue("title", res.title);
      videoForm.setValue("theme", res.theme);
      videoForm.setValue("target", res.target);
      setStep("details");
    }
    
    setIsLoading(false);
  }

  async function onVideoSubmit(values: z.infer<typeof videoFormSchema>) {
    if (isLoading) return;
    setIsLoading(true);

    if (session.data?.user.id) {
      await serverFn(t, onCreateVideoFromDescription, values);
      handleClose();
      router.refresh();
    } else {
      router.push("/login?onboarding=true");
    }
    
    setIsLoading(false);
  }

  function handleClose() {
    setIsOpen(false);
    setStep("description");
    initialForm.reset();
    videoForm.reset();
  }

  function handleBack() {
    setStep("description");
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button>
          <PlusIcon className="h-4 w-4" />
          {t("Create Video")}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{t("Create New Video")}</DialogTitle>
        </DialogHeader>

        {step === "description" ? (
          <Form {...initialForm}>
            <form onSubmit={initialForm.handleSubmit(onInitialSubmit)} className="space-y-4">
              <TextareaField
                control={initialForm.control}
                name="description"
                label={t("Video Description")}
                description={t("Describe what you want your video to be about")}
                input={{
                  props: {
                    placeholder: t("e.g. A tutorial about cooking pasta, or a promotional video for my new product"),
                    rows: 4,
                  },
                }}
              />
              <div className="flex gap-2 justify-end">
                <Button type="button" variant="outline" onClick={handleClose}>
                  {t("Cancel")}
                </Button>
                <Button type="submit" loading={isLoading} disabled={isLoading}>
                  {t("Generate Ideas")}
                </Button>
              </div>
            </form>
          </Form>
        ) : (
          <Form {...videoForm}>
            <form onSubmit={videoForm.handleSubmit(onVideoSubmit)} className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>{t("Video Details")}</CardTitle>
                  <CardDescription>
                    {t("Review and edit the generated details for your video")}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Field
                    control={videoForm.control}
                    name="title"
                    label={t("Title")}
                  />
                  <Field
                    control={videoForm.control}
                    name="theme"
                    label={t("Theme")}
                    description={t("Topic and format of the video")}
                  />
                  <Field
                    control={videoForm.control}
                    name="target"
                    label={t("Target Audience")}
                    description={t("Who is this video for?")}
                  />
                </CardContent>
              </Card>
              <div className="flex gap-2 justify-end">
                <Button type="button" variant="outline" onClick={handleBack}>
                  {t("Back")}
                </Button>
                <Button type="button" variant="outline" onClick={handleClose}>
                  {t("Cancel")}
                </Button>
                <Button type="submit" loading={isLoading} disabled={isLoading}>
                  {t("Create Video")}
                </Button>
              </div>
            </form>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  );
}
