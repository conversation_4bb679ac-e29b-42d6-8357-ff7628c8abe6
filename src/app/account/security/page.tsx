"use client";

import { PasswordField } from "@/components/Field";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form } from "@/components/ui/form";
import { authClient } from "@/lib/auth-client";
import { serverFn } from "@/lib/serverFn";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import z from "zod";
import { onSetPassword } from "./security.action";

type Account = {
  id: string;
  providerId: string;
  createdAt: Date;
  updatedAt: Date;
  accountId: string;
  scopes: string[];
};

const formSchema = z.object({
  currentPassword: z.string().min(8),
  newPassword: z.string().min(8),
  confirmPassword: z.string().min(8),
});

export default function Page() {
  const t = useTranslations();
  const [isLoading, setIsLoading] = useState(false);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const hasEmailAccount = accounts.some((x) => x.providerId === "credential");

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: (values, context, options) => {
      if (!hasEmailAccount) {
        return zodResolver(formSchema.omit({ currentPassword: true }) as typeof formSchema)(values, context, options);
      }
      return zodResolver(formSchema)(values, context, options);
    },
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  useEffect(() => {
    setIsLoading(true);
    authClient.listAccounts().then((res) => {
      if (res.error) {
        console.error(res.error);
        toast.error(t("Failed to list accounts"));
        return;
      } else setAccounts(res.data);
      setIsLoading(false);
    });
  }, [t]);

  const handleChangePassword = async (passwordData: z.infer<typeof formSchema>) => {
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast.error(t("New passwords don't match!"));
      return;
    }

    setIsLoading(true);
    try {
      if (hasEmailAccount) {
        await authClient.changePassword(passwordData);
      } else {
        await serverFn(t, onSetPassword, passwordData);
      }
      await authClient.changePassword(passwordData);
      form.reset();
      toast.success(t("Password changed successfully!"));
    } catch (error) {
      console.error("Error changing password:", error);
      toast.error(t("Error changing password"));
    } finally {
      setIsLoading(false);
    }
  };
  return (
    <Card>
      {hasEmailAccount ? (
        <CardHeader>
          <CardTitle>{t("Change Password")}</CardTitle>
          <CardDescription>{t("Update your password to keep your account secure")}</CardDescription>
        </CardHeader>
      ) : (
        <CardHeader>
          <CardTitle>{t("Set Password")}</CardTitle>
          <CardDescription>{t("Set a password to login with email and password")}</CardDescription>
        </CardHeader>
      )}
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleChangePassword)} className="space-y-4">
            {hasEmailAccount && (
              <PasswordField control={form.control} name="currentPassword" label={t("Current Password")} />
            )}
            <PasswordField control={form.control} name="newPassword" label={t("New Password")} />
            <PasswordField control={form.control} name="confirmPassword" label={t("Confirm New Password")} />

            <Button type="submit" disabled={isLoading} loading={isLoading}>
              {t("Submit")}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
