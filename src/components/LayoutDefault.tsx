"use client";

import { Notifications } from "@/components/Notifications";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { authClient } from "@/lib/auth-client";
import { cn } from "@/lib/utils";
import Cookies from "js-cookie";
import {
  CreditCardIcon,
  EditIcon,
  FileBoxIcon,
  LanguagesIcon,
  LogInIcon,
  MenuIcon,
  UserIcon,
  UsersIcon,
  Video,
} from "lucide-react";
import { useLocale, useTranslations } from "next-intl";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { ReactNode, useEffect, useState } from "react";
import z from "zod";
import { en, fr } from "zod/locales";
import { But<PERSON> } from "./ui/button";
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from "./ui/sheet";

export default function LayoutDefault({ children }: { children: React.ReactNode }) {
  return (
    <div className={"flex flex-col h-screen"}>
      <div className="flex flex-col flex-grow">
        <TopBar />
        <Content>{children}</Content>
      </div>
    </div>
  );
}

function NavLink({ children, ...props }: { children: React.ReactNode } & React.ComponentProps<typeof Link>) {
  const urlPathname = usePathname();

  return (
    <Button variant="ghost" asChild>
      <Link
        {...props}
        className={cn(props.className, urlPathname.startsWith(props.href.toString()) && "bg-gray-500/20")}
      >
        {children}
      </Link>
    </Button>
  );
}

function NavDropdownLink({ children, ...props }: { children: React.ReactNode } & React.ComponentProps<typeof Link>) {
  const urlPathname = usePathname();

  return (
    <Link
      {...props}
      className={cn(
        "flex items-center gap-2",
        props.className,
        urlPathname.startsWith(props.href.toString()) && "bg-gray-500/20",
      )}
    >
      {children}
    </Link>
  );
}

function MobileMenu({ children }: { children: ReactNode }) {
  const t = useTranslations();
  const [open, setOpen] = useState(false);
  const pathname = usePathname();

  useEffect(() => {
    setOpen(false);
  }, [pathname]);

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon">
          <MenuIcon className="size-6" />
        </Button>
      </SheetTrigger>
      <SheetContent side="left">
        <SheetHeader>
          <SheetTitle>{t("Menu")}</SheetTitle>
        </SheetHeader>
        <div className="flex flex-col gap-4 p-2 items-stretch">{children}</div>
      </SheetContent>
    </Sheet>
  );
}

function TopBar() {
  const t = useTranslations();
  const session = authClient.useSession();
  const locale = useLocale();

  const avatar = session.data && (
    <Avatar className="w-6 h-6">
      <AvatarImage src={session.data.user.image || undefined} alt={session.data.user.name} />
      <AvatarFallback>
        <UserIcon />
      </AvatarFallback>
    </Avatar>
  );
  async function logout() {
    await authClient.signOut();
    localStorage.clear();
    window.location.reload();
  }
  let menu;
  let iconsMenu;
  if (session.data?.user.role === "admin") {
    menu = (
      <>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="text-start justify-start">
              <EditIcon /> {t("Management")}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="min-w-[var(--radix-dropdown-menu-trigger-width)]">
            {(
              [
                [t("Prompts"), "/admin/prompts", FileBoxIcon],
                [t("Packages"), "/admin/packages", CreditCardIcon],
                [t("Translations"), "/admin/translations", LanguagesIcon],
                [t("Users"), "/admin/users", UsersIcon],
              ] as const
            ).map(([label, link, Icon]) => (
              <DropdownMenuItem key={label} asChild>
                <NavDropdownLink href={link}>
                  <Icon /> {label}
                </NavDropdownLink>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </>
    );
    iconsMenu = (
      <>
        <Notifications />
        <DropdownMenu>
          <DropdownMenuTrigger>{avatar}</DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuLabel>{session.data.user.email}</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link href="/admin/account">{t("Account")}</Link>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={logout}>{t("logout")}</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </>
    );
  } else if (session.data?.user) {
    menu = (
      <>
        <NavLink href="/videos">
          <Video />
          {t("Videos")}
        </NavLink>
      </>
    );
    iconsMenu = (
      <>
        <Notifications />
        <DropdownMenu>
          <DropdownMenuTrigger>{avatar}</DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuLabel>{session.data.user.email}</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <NavDropdownLink href="/account/profile">{t("Account")}</NavDropdownLink>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <NavDropdownLink href="/packages">{t("Manage Packages")}</NavDropdownLink>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={logout}>{t("Logout")}</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </>
    );
  } else {
    menu = (
      <NavLink href="/login">
        <LogInIcon />
        {t("Login")}
      </NavLink>
    );
  }
  const langSelector = (
    <Select
      defaultValue={locale}
      onValueChange={(locale) => {
        Cookies.set("locale", locale);
        z.config(locale === "en" ? en() : fr());
        window.location.reload();
      }}
    >
      <SelectTrigger className="max-md:w-full font-mono">
        <SelectValue />
      </SelectTrigger>
      <SelectContent className="font-mono">
        <SelectItem value="en">EN 🇬🇧</SelectItem>
        <SelectItem value="fr">FR 🇫🇷</SelectItem>
      </SelectContent>
    </Select>
  );

  return (
    <div className="bg-gray-50">
      <div className="max-w-5xl m-auto px-5 py-3 flex items-center gap-4 relative">
        <Link href="/" className="text-3xl me-auto">
          Shorts
        </Link>
        <div className={cn("flex items-center gap-4 max-md:hidden")}>
          {menu}
          {iconsMenu}
          {langSelector}
        </div>
        <div className={cn("flex items-center gap-4 md:hidden")}>
          {iconsMenu}
          <MobileMenu>
            {langSelector}
            {menu}
          </MobileMenu>
        </div>
      </div>
    </div>
  );
}

function Content({ children }: { children: React.ReactNode }) {
  const urlPathname = usePathname();
  return (
    <div id="page-container" className={cn("overflow-auto", urlPathname === "/login" ? "my-auto " : "flex-1")}>
      <div id="page-content" className={"max-w-5xl m-auto p-5 pb-12"}>
        {children}
      </div>
    </div>
  );
}
