import { spawn } from "child_process";
import { access } from "fs/promises";

interface ThumbnailOptions {
  input: string;
  output: string;
  timestamp?: number; // seconds, default 1s
  ogWidth?: number; // default 1200
  ogHeight?: number; // default 630
}

export async function createOgThumbnail(options: ThumbnailOptions): Promise<void> {
  const {
    input,
    output,
    // timestamp = 1,
    // ogWidth = 1200,
    // ogHeight = 630,
  } = options;

  // Check input exists
  await access(input).catch(() => {
    throw new Error(`Input file not found: ${input}`);
  });

  // ffmpeg args
  // const vf = `select='gte(t,${timestamp})',scale=-1:${ogHeight},pad=${ogWidth}:${ogHeight}:(ow-iw)/2:(oh-ih)/2`;
  const vf = "thumbnail";

  const args = ["-i", input, "-vf", vf, "-frames:v", "1", "-update", "1", output];

  return new Promise((resolve, reject) => {
    const ffmpeg = spawn("ffmpeg", args, { stdio: "inherit" });

    ffmpeg.on("error", (err) => reject(new Error(`Failed to start ffmpeg: ${err.message}`)));
    ffmpeg.on("close", (code) => {
      if (code === 0) resolve();
      else reject(new Error(`ffmpeg exited with code ${code}`));
    });
  });
}
