import { useTranslations } from "next-intl";
import { toast } from "sonner";
import { errors } from "./errors";

export async function serverFn<T extends (...args: never[]) => Promise<any>>(
  t: ReturnType<typeof useTranslations>,
  fn: T,
  ...args: Parameters<T>
): Promise<
  (T extends (...args: never[]) => Promise<{ error: errors } | undefined | infer Return> ? Return : never) | undefined
> {
  try {
    const res = await fn(...args);

    if (res?.error) {
      toast.error(t(res.error));
      return undefined;
    }
    return res;
  } catch (error) {
    console.error(error);
    toast.error(errors.InternalServerError);
  }
  return undefined;
}

export type TFReturn<T extends (...args: never[]) => Promise<any>> = T extends (
  ...args: never[]
) => Promise<{ error: errors } | undefined | infer Return>
  ? Return
  : never;
