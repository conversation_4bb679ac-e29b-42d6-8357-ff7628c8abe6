import { dbInit } from "@/database/drizzle/db";
import { getPrompt } from "@/database/drizzle/queries/prompts";
import { sectionSchema } from "@/database/drizzle/schema/videos";
import { clone, mergeWith, pick } from "es-toolkit";
import Handlebars from "handlebars";
import z from "zod";

export async function getAIOptions<T extends keyof typeof prompts>(
  name: T,
  params: Record<keyof (typeof prompts)[T]["paramDescriptions"], string | number>,
) {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { paramDescriptions, ...defaultOptions } = prompts[name];
  const db = dbInit();
  const [dbPrompt] = await getPrompt(db, name);
  const { model, prompt, maxTokens, schema } = mergeWith(
    clone(defaultOptions),
    pick(dbPrompt || {}, ["model", "prompt", "maxTokens"]),
    (targetValue, sourceValue) => sourceValue && targetValue,
  );

  return {
    model,
    maxTokens,
    prompt: Handlebars.compile(prompt)(params),
    schema,
  };
}

const transcriptFromAISchema = z.object({
  description: z.string(),
  sections: z.array(sectionSchema.omit({ imageUrl: true })),
});

export const prompts = {
  TargetSuggestion: {
    model: "sonar",
    prompt: `Suggest 3 possible target audiences for videos of theme {{theme}} in the shortest possible form.`,
    maxTokens: 10000,
    schema: z.object({ suggestions: z.array(z.string()) }),
    paramDescriptions: {
      theme: "Topic and format of the videos, e.g. promo video about your product or tutorial about a topic",
    },
  },
  VideoTitles: {
    model: "sonar",
    prompt: `Give exactly {{noOfVideos}} titles for videos 
      of theme {{theme}} in short video format for {{target}}.`,
    maxTokens: 10000,
    schema: z.object({ titles: z.array(z.string()) }),
    paramDescriptions: {
      noOfVideos: "The exact number of video titles to generate.",
      theme: "Topic and format of the videos, e.g. promo video about your product or tutorial about a topic",
      target: "Target audience of the videos",
    },
  },
  VideoTranscript: {
    model: "sonar-pro",
    prompt: `Create a script for a video titled {{title}} in theme {{theme}} in short video format for {{target}}.
      Follow the following instructions, respond with JSON in the provided response format:
      Aim for exactly {{wordCount}} words to match {{durationMinutes}} minutes of narration at 150 WPM. Short sentences. Natural voiceover style.
      Give a concise description also.`,
    maxTokens: 10000,
    schema: transcriptFromAISchema,
    paramDescriptions: {
      title: "The title of the video",
      theme: "The theme or type of the videos (e.g., ad, tutorial, tech review, fun fact).",
      target: "What the video is about (e.g., a product, a topic).",
      wordCount: "The exact number of words in the script, matching narration length.",
      durationMinutes: "The target duration of the video in minutes.",
    },
  },
} as const;
