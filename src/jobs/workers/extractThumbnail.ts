import { dbInit } from "@/database/drizzle/db";
import { uploadFileStream } from "@/database/drizzle/queries/files";
import { updateJob } from "@/database/drizzle/queries/jobs";
import * as drizzleQueries from "@/database/drizzle/queries/videos";
import { QExtractThumbnail, w } from "@/jobs/queues";
import { createOgThumbnail } from "@/lib/ffmpeg";
import axios from "axios";
import fs from "fs";
import path from "path";
import { promisify } from "util";

const writeFile = promisify(fs.writeFile);
const unlink = promisify(fs.unlink);

export const WExtractThumbnail = w(QExtractThumbnail, async (job) => {
  try {
    const db = dbInit();
    const { data } = job;

    await job.updateProgress({ value: 1, message: "Fetching video record" });

    // Get the video record
    let [record] = await drizzleQueries.getVideo(db, data.videoId);
    if (!record) {
      throw new Error(`Video with id ${data.videoId} not found`);
    }
    record = (
      await drizzleQueries
        .updateVideo(db, record.id, {
          jobId: job.id,
        })
        .returning()
    )[0];

    // Check if video has a videoUrl
    if (!record.videoUrl) {
      console.log(`Video ${data.videoId} does not have a videoUrl, stopping thumbnail extraction`);
      await updateJob(db, job.id!, { status: "completed", endedAt: new Date() });
      return { message: "No videoUrl found, skipping thumbnail extraction" };
    }

    await job.updateProgress({ value: 2, message: "Downloading video" });

    // Download the video file
    const videoResponse = await axios.get(record.videoUrl, {
      responseType: "arraybuffer",
    });

    if (!videoResponse.data) {
      throw new Error("Failed to download video");
    }

    try {
      const { thumbnailUrl, thumbnailId, thumbnailPath, videoPath } = await getThumbnail(
        db,
        record.userId!,
        record.videoUrl,
        record.id,
      );
      record = (
        await drizzleQueries
          .updateVideo(db, record.id, {
            thumbnailUrl: thumbnailUrl,
          })
          .returning()
      )[0];

      // Clean up temporary files
      await Promise.allSettled([unlink(videoPath), unlink(thumbnailPath)]);

      return {
        message: "Thumbnail extracted successfully",
        thumbnailUrl,
        thumbnailId,
      };
    } catch (ffmpegError) {
      throw ffmpegError;
    }
  } catch (error) {
    console.error("Error in thumbnail extraction worker:", error);
  }
});

export async function getThumbnail(db: DB, userId: string, videoUrl: string, videoId: string) {
  const res = await fetch(videoUrl);
  const videoBuffer = Buffer.from(await res.arrayBuffer());
  const videoBaseName = `shorts-${videoId}`;
  const tempDir = "/tmp";
  const now = Date.now();
  const videoFileName = `video-${videoId}-${now}.mp4`;
  const thumbnailFileName = `thumbnail-${videoId}-${now}.jpg`;
  const videoPath = path.join(tempDir, videoFileName);
  const thumbnailPath = path.join(tempDir, thumbnailFileName);

  try {
    // Write video to temporary file
    await writeFile(videoPath, videoBuffer);

    await createOgThumbnail({
      input: videoPath,
      output: thumbnailPath,
    });

    // Read the generated thumbnail
    const thumbnailStream = fs.createReadStream(thumbnailPath);

    // Upload thumbnail using the same filename as video but with .thumbnail extension
    const thumbnailUploadName = `${videoBaseName}.thumbnail.jpg`;

    const thumbnailFile = await uploadFileStream(db, userId!, thumbnailStream, thumbnailUploadName, "image/jpg");

    return {
      message: "Thumbnail extracted successfully",
      thumbnailUrl: thumbnailFile.url,
      thumbnailId: thumbnailFile.id,
      thumbnailPath,
      videoPath,
    };
  } catch (ffmpegError) {
    // Clean up temporary files in case of error
    await Promise.allSettled([unlink(videoPath).catch(() => {}), unlink(thumbnailPath).catch(() => {})]);
    throw ffmpegError;
  }
}
