import { dbInit } from "@/database/drizzle/db";
import { uploadFile, uploadFileStream } from "@/database/drizzle/queries/files";
import { updateJob } from "@/database/drizzle/queries/jobs";
import * as drizzleQueries from "@/database/drizzle/queries/videos";
import { FileItem } from "@/database/drizzle/schema/files";
import { defaultVideoSettings, transcriptSchema, VideoInsert, VoiceoverData } from "@/database/drizzle/schema/videos";
import { QGenerateVideo, w } from "@/jobs/queues";
import { ai } from "@/lib/ai";
import { createSpeechWithTiming } from "@/lib/elevenlabs";
import { getAIOptions } from "@/lib/prompts";
import { searchPhotos } from "@/lib/searchPhotos";
import { CharacterAlignmentResponseModel } from "@elevenlabs/elevenlabs-js/api";
import axios, { AxiosError } from "axios";
import { toMerged } from "es-toolkit";
import { createReadStream } from "fs";
import { unlink } from "fs/promises";
import { htmlToText } from "html-to-text";
import { marked } from "marked";
import { z } from "zod";
import { getThumbnail } from "./extractThumbnail";

export const WGenerateVideo = w(QGenerateVideo, async (job) => {
  try {
    const db = dbInit();
    const { data } = job;

    let [record] = await drizzleQueries.getVideo(db, data.videoId);

    record = (
      await drizzleQueries
        .updateVideo(db, record.id, {
          jobId: job.id,
        })
        .returning()
    )[0];

    await job.updateProgress({ value: 0, message: "Generating transcript" });
    /**
     * Generate transcript
     */
    let transcript;
    if (record.transcript?.sections?.length) {
      transcript = record.transcript as z.infer<typeof transcriptSchema>;
    } else {
      const [_transcript, searchResults] = await getTranscript(record);
      await Promise.allSettled(
        _transcript.sections.map(async (x) => {
          x.voiceover = await marked.parse(x.voiceover.replace(/^[\u200B\u200C\u200D\u200E\u200F\uFEFF]/, ""));
          return x;
        }),
      );
      transcript = _transcript;

      /**
       * Save transcript
       */
      record = (
        await drizzleQueries
          .updateVideo(db, record.id, {
            transcript: _transcript,
            searchResults: searchResults,
          })
          .returning()
      )[0];
    }

    await job.updateProgress({ value: 1, message: "Generating Images" });
    /**
     * Get images
     */
    console.log("Started");
    await Promise.allSettled(
      transcript.sections.map(async (x, i) => {
        console.log("image", i);
        if (x.imageUrl) {
          const dataUrlRegex = /^data:(.+);base64,(.*)$/;

          const match = x.imageUrl.match(dataUrlRegex);
          if (match) {
            const base64Data = match[2];
            const buffer = Buffer.from(base64Data, "base64");
            console.log("uploading", i);
            const res = await uploadFile(db, record.userId!, buffer, `shorts-${record.id}-image-${i}.jpg`);
            console.log("uploaded", res);
            x.imageUrl = res.url;
            return res.url;
          }
          return x.imageUrl;
        }
        let url;
        try {
          const results = await searchPhotos({ query: x.imageDescription, page: 1, perPage: 1 });
          if (results?.length) {
            url = results[0].url;
          }
        } catch (error) {
          console.warn(error);
        }
        x.imageUrl = url;
        return url;
      }),
    );

    /**
     * Save images
     */
    record = (await drizzleQueries.updateVideo(db, record.id, { transcript: transcript }).returning())[0];

    await job.updateProgress({ value: 2, message: "Generating Audio" });
    /**
     * Generate audio
     */
    const { audioBase64, ...timing } = await createSpeechWithTiming(
      record.voiceId || "tnSpp4vdxKPjI9w0GnoV",
      transcript.sections
        .map((x) => htmlToText(x.voiceover))
        .join(" ")
        .replaceAll(/\[\d+\]/g, ""),
    );

    await job.updateProgress({ value: 3, message: "Uploading Audio" });
    const audioFile = await uploadFile(
      db,
      record.userId!,
      Buffer.from(audioBase64, "base64"),
      `shorts-${record.id}.mp3`,
    );

    /**
     * Save Audio
     */
    const voiceoverData: VoiceoverData = {
      audioURL: audioFile.url,
      alignment: timing.alignment,
    };
    record = (
      await drizzleQueries
        .updateVideo(db, record.id, {
          voiceoverData: voiceoverData,
        })
        .returning()
    )[0];

    /**
     * Calculate word and image timings
     */
    const clipItems = timing.alignment
      ? getClipItems(transcript, timing.alignment)
      : {
          sections: [],
          words: [],
        };

    await job.updateProgress({ value: 4, message: "Generating Video" });
    /**
     * Render video
     */
    const videoURL = await generateVideo(audioFile, clipItems, record.videoSettings || {});

    /**
     * Save video
     */
    if (videoURL) {
      const { thumbnailPath, videoPath } = await getThumbnail(db, record.userId!, videoURL, record.id);
      const videoFile = await uploadFileStream(
        db,
        record.userId!,
        createReadStream(videoPath),
        `shorts-${record.id}.mp4`,
        "video/mp4",
      );
      // Clean up temporary files
      await Promise.allSettled([unlink(videoPath), unlink(thumbnailPath)]);
      record = (
        await drizzleQueries
          .updateVideo(db, record.id, {
            videoUrl: videoFile.url,
          })
          .returning()
      )[0];
    }

    await job.updateProgress({ value: 5, message: "Complete" });
    await updateJob(db, job.id!, { status: "completed", endedAt: new Date() });
    return record;
  } catch (error) {
    console.error(error);
  }
});

async function getTranscript(data: VideoInsert) {
  const durationMinutes = (data.durationSeconds || 90) / 60;

  const { output, searchResults } = await ai.getStructuredOutput(
    await getAIOptions("VideoTranscript", {
      title: data.title!,
      theme: data.theme,
      target: data.target,
      wordCount: durationMinutes * 150,
      durationMinutes,
    }),
  );

  return [output as z.infer<typeof transcriptSchema>, searchResults] as const;
}

function getClipItems(transcript: z.infer<typeof transcriptSchema>, alignment: CharacterAlignmentResponseModel) {
  const elevenAlignment = alignment.characters.map((x, i) => ({
    char: x,
    start: alignment.characterStartTimesSeconds[i],
    end: alignment.characterEndTimesSeconds[i],
  }));

  let i = 0;
  const sections = transcript.sections.map((x) => ({ ...x, start: -1, end: 0 }));
  const words = transcript.sections
    .map((x, sectionIndex) =>
      htmlToText(x.voiceover)
        .split(/\s+/g)
        .map((word) => ({
          text: word,
          imageUrl: x.imageUrl,
          sectionIndex,
        })),
    )
    .flat()
    .filter((x) => x)
    .map((word) => {
      const charAlignmentsInWord: {
        char: string;
        start?: number;
        end?: number;
        notAudio?: boolean;
      }[] = [];
      let remaining = word.text;

      while (i < elevenAlignment.length && elevenAlignment[i].char === " ") i++;
      while (i < elevenAlignment.length && remaining) {
        const htmlBit = remaining.match(/^<[^>]+>/);
        if (htmlBit?.[0]) {
          charAlignmentsInWord.push({ char: htmlBit[0], notAudio: true });
          remaining = remaining.slice(htmlBit[0].length);
          continue;
        }

        const { char, start, end } = elevenAlignment[i];
        const index = remaining.indexOf(char);

        if (index === -1) {
          // Alignment char not found, skip
          i++;
          continue;
        }

        if (index > 0) {
          // Push the prefix as notAudio
          charAlignmentsInWord.push({
            char: remaining.slice(0, index),
            notAudio: true,
          });
        }

        // Push the matched char from alignment
        charAlignmentsInWord.push({ char, start, end });

        // Chop the matched part from the remaining transcript
        remaining = remaining.slice(index + char.length);
        i++;
      }
      // If anything is left in the transcript after the last alignment match
      if (remaining.length > 0) {
        charAlignmentsInWord.push({ char: remaining, notAudio: true });
      }
      return {
        ...word,
        alignment: charAlignmentsInWord,
        start: charAlignmentsInWord.find((x) => !x.notAudio)?.start || 0,
        end: charAlignmentsInWord.findLast((x) => !x.notAudio)?.end || 0,
      };
    })
    .filter((x) => x.start !== undefined && x.end !== undefined);
  words.forEach((x) => {
    const st = sections[x.sectionIndex];
    if (st.start === -1 && x.start !== undefined) st.start = x.start;
    if (x.end !== undefined) st.end = x.end;
  });
  if (sections[0]) sections[0].start = 0;
  sections.forEach((x, i) => {
    if (i + 1 < sections.length) {
      x.end = Math.max(sections[i + 1].start, x.end);
    }
  });
  return { words, sections };
}

const GENERATE_VIDEO_TIMEOUT = 60_000_000; // 1000 minutes
const POLL_INTERVAL = 60_000; // 1 minute

async function generateVideo(
  audioFile: FileItem,
  clipItems: ReturnType<typeof getClipItems>,
  _videoSettings: Partial<typeof defaultVideoSettings>,
) {
  const videoSettings = toMerged(defaultVideoSettings, _videoSettings);
  const shotstackResponse = await axios
    .post(
      `https://api.shotstack.io/edit/stage/render`,
      {
        timeline: {
          background: "#FFFFFF",
          tracks: [
            {
              clips: [
                {
                  length: "end",
                  asset: {
                    type: "audio",
                    src: audioFile.url,
                    volume: 1,
                  },
                  start: 0,
                },
              ],
            },
            ...clipItems.words.map((word) => ({
              clips: [
                {
                  asset: {
                    type: "text",
                    text: word.text,
                    alignment: {
                      horizontal: "center",
                      vertical: "center",
                    },
                    font: videoSettings.text.font,
                    width: 576,
                    height: 576,
                    stroke: videoSettings.text.stroke,
                  },
                  start: word.start,
                  length: word.end - word.start,
                  transition: {
                    in: "slideUpFast",
                  },
                },
              ],
            })),
            ...clipItems.sections.map((section) => ({
              clips: [
                {
                  length: section.end - section.start,
                  asset: {
                    type: "image",
                    src: section.imageUrl,
                  },
                  start: section.start,
                  effect: "zoomInFast",
                },
              ],
            })),
          ],
        },
        output: {
          format: "mp4",
          fps: 25,
          size: {
            width: 576,
            height: 1024,
          },
        },
      },
      {
        headers: {
          "x-api-key": process.env.SHOTSTACK_API_KEY,
          "Content-Type": "application/json",
        },
      },
    )
    .catch((e) => console.log((e as AxiosError).toJSON()));

  if (!shotstackResponse?.data.response.id) return null;

  const start = Date.now();
  while (Date.now() - start < GENERATE_VIDEO_TIMEOUT) {
    const videoResponse = await axios
      .get(`https://api.shotstack.io/edit/stage/render/${shotstackResponse?.data.response.id}`, {
        headers: {
          "x-api-key": process.env.SHOTSTACK_API_KEY,
          "Content-Type": "application/json",
        },
      })
      .catch((e) => console.log((e as AxiosError).toJSON()));

    if (!videoResponse) return null;
    if (videoResponse?.data.response.status === "failed") {
      console.log("Video render failed, stopping polling.");
      return null;
    }

    console.log(new Date().toTimeString(), videoResponse?.data.response.status);
    await new Promise((resolve) => setTimeout(resolve, POLL_INTERVAL));

    if (videoResponse?.data.response.status === "done") {
      return videoResponse?.data.response.url;
    }
  }
}
