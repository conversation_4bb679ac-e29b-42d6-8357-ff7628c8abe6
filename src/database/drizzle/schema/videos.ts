import { CharacterAlignmentResponseModel } from "@elevenlabs/elevenlabs-js/api";
import { relations } from "drizzle-orm";
import { integer, jsonb, pgTable, text, timestamp, uuid, varchar } from "drizzle-orm/pg-core";
import z from "zod";
import { user } from "./auth";
import { jobTable } from "./jobs";

export const sectionSchema = z.object({
  imageDescription: z.string(),
  voiceover: z.string(),
  imageUrl: z.string().optional(),
});

export const transcriptSchema = z.object({ sections: z.array(sectionSchema) });

export type SearchResults =
  | {
      title: string;
      url: string;
      date: string | null;
    }[]
  | null;

export type VoiceoverData = {
  audioURL: string;
  alignment?: CharacterAlignmentResponseModel;
};

export const defaultVideoSettings = {
  text: {
    font: {
      color: "#ffffff",
      family: "Permanent Marker",
      size: "72",
      lineHeight: 1,
    },
    stroke: {
      width: 5,
      color: "#000000",
    },
  },
};

export const videoSettingsSchema = z
  .object({
    text: z.object({
      font: z.object({
        color: z.string(),
        family: z.string(),
        size: z.string(),
        lineHeight: z.number(),
      }),
      stroke: z.object({
        width: z.number(),
        color: z.string(),
      }),
    }),
  })
  .catch(defaultVideoSettings);

export const videoIdeaTable = pgTable("videos_idea", {
  id: uuid().primaryKey().defaultRandom(),
  theme: varchar({ length: 255 }).notNull(),
  target: text().notNull(),
  frequency: integer().notNull(),
  days: integer().notNull(),
  videos: jsonb().$type<{ title: string }[]>(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at")
    .defaultNow()
    .$onUpdate(() => /* @__PURE__ */ new Date())
    .notNull(),
  startedAt: timestamp(),
  userId: text().references(() => user.id, { onDelete: "cascade" }),
});

export type VideoIdeaItem = typeof videoIdeaTable.$inferSelect;
export type VideoIdeaInsert = typeof videoIdeaTable.$inferInsert;

export const videoTable = pgTable("videos", {
  id: uuid().primaryKey().defaultRandom(),
  theme: varchar({ length: 255 }).notNull(),
  target: text().notNull(),
  title: text(),
  description: text(),
  transcript: jsonb().$type<z.infer<typeof transcriptSchema>>(),
  searchResults: jsonb().$type<SearchResults>(),
  durationSeconds: integer().default(90),
  voiceoverData: jsonb().$type<VoiceoverData>(),
  voiceId: text(),
  videoSettings: jsonb().$type<Partial<z.infer<typeof videoSettingsSchema>>>().default({}),
  videoUrl: varchar({ length: 255 }),
  thumbnailUrl: varchar({ length: 255 }),
  visibility: text({ enum: ["public", "private"] })
    .notNull()
    .default("public"),
  scheduledAt: timestamp("scheduled_at"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at")
    .defaultNow()
    .$onUpdate(() => /* @__PURE__ */ new Date())
    .notNull(),
  jobId: uuid().references(() => jobTable.id, { onDelete: "cascade" }),
  userId: text().references(() => user.id, { onDelete: "cascade" }),
  videoIdeaId: uuid().references(() => videoIdeaTable.id, { onDelete: "cascade" }),
});

export type VideoItem = typeof videoTable.$inferSelect;
export type VideoInsert = typeof videoTable.$inferInsert;

export const userRelations = relations(user, ({ many }) => ({
  videos: many(videoTable),
}));

export const videoRelations = relations(videoTable, ({ one }) => ({
  job: one(jobTable, {
    fields: [videoTable.jobId],
    references: [jobTable.id],
  }),
  user: one(user, {
    fields: [videoTable.userId],
    references: [user.id],
  }),
  videoIdea: one(videoIdeaTable, {
    fields: [videoTable.videoIdeaId],
    references: [videoIdeaTable.id],
  }),
}));

export const jobRelations = relations(jobTable, ({ one }) => ({
  video: one(videoTable),
}));

export const videoIdeaRelations = relations(videoIdeaTable, ({ many }) => ({
  videos: many(videoTable),
}));
